Creating a directed graph ...  ok.
Adding vertex with data item: 'A' ... ok.
Adding vertex with data item: 'B' ... ok.
Adding vertex with data item: 'C' ... ok.
Adding edge 0 for vertices ('A','B') with data item: '0.5' ... ok.
Adding edge 1 for vertices ('B','C') with data item: '0.7' ... ok.
Adding edge 2 for vertices ('C','A') with data item: '0.25' ... ok.
Vertices: A B C
Edges: A:B B:C C:A
Moving vertex iterator to vertex with key 'B' ... ok.
Removing current vertex 'B' ... ok.
Vertices: A C
Edges: C:A
