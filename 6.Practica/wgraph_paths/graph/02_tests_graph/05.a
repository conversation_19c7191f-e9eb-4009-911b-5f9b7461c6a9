Creating a directed graph ...  ok.
Adding vertex with data item: 'A' ... ok.
Adding vertex with data item: 'B' ... ok.
Adding edge 0 for vertices ('A','B') with data item: '0.5' ... ok.
Adding edge 1 for vertices ('B','A') with data item: '0.7' ... ok.
Current vertex with label 1 visited flag: F data: 'B' data key: 'B'.
Moving vertex iterator to vertex with key 'A' ... ok.
Current vertex with label 0 visited flag: F data: 'A' data key: 'A'.
Current edge ('A','B'), visited flag: F, data: 0.5
Moving vertex iterator to vertex with key 'B' ... ok.
Current vertex with label 1 visited flag: F data: 'B' data key: 'B'.
Current edge ('B','A'), visited flag: F, data: 0.7
