Creating a directed graph ...  ok.
Adding vertex with data item: 'A' ... ok.
Adding vertex with data item: 'B' ... ok.
Adding vertex with data item: 'C' ... ok.
Adding vertex with data item: 'B' ... ok.
Finding vertex with key 'B' ...  found.
Current vertex with label 1 visited flag: F data: 'B' data key: 'B'.
Finding next vertex with key 'B' ...  found.
Current vertex with label 3 visited flag: F data: 'B' data key: 'B'.
Finding next vertex with key 'B' ...  didn't found.
Current vertex doesn't exist.
