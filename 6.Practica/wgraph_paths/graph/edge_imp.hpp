/**
 * @file edge_imp.hpp
 *
 * CopyRight F. J. Madrid-Cuevas <<EMAIL>>
 *
 * Sólo se permite el uso de este código en la docencia de las asignaturas sobre
 * Estructuras de Datos de la Universidad de Córdoba.
 *
 * Está prohibido su uso para cualquier otro objetivo.
 */
#pragma once
#include <cassert>
// edge.hpp se incluye desde donde se usa Edge<T,E> o en el .hpp principal del
// grafo. No es estrictamente necesario aquí si la estructura de inclusión es
// correcta. #include <edge.hpp>

template <class T, class E>
Edge<T, E>::Edge(VertexRef const &u, VertexRef const &v, E const &data) {
  // TODO
  u_ = u;
  v_ = v;
  item_ = data;
  is_visited_ = false;
  //
  assert(has(u));
  assert(has(v));
  assert(other(u) == v);
  assert(other(v) == u);
  assert(first() == u);
  assert(second() == v);
  assert(item() == data);
  assert(!is_visited());
}

template <class T, class E>
typename Edge<T, E>::Ref Edge<T, E>::create(VertexRef const &u,
                                            VertexRef const &v, E const &data) {
  return std::make_shared<Edge<T, E>>(u, v, data);
}

template <class T, class E> Edge<T, E>::~Edge() {
  // Destructor: No se necesita código explícito si los miembros
  // son punteros inteligentes o tipos que gestionan su propia memoria.
}

template <class T, class E> bool Edge<T, E>::is_visited() const {
  // TODO: fixme
  return is_visited_;
  //
}

template <class T, class E> E const &Edge<T, E>::item() const {
  // TODO: fixme
  return item_;
  //
}

template <class T, class E> bool Edge<T, E>::has(VertexRef const &n) const {
  // TODO: fixme
  // Check if n is one of the vertices of the edge.
  return (n == u_ || n == v_);
  //
}

template <class T, class E>
typename Edge<T, E>::VertexRef const &
Edge<T, E>::other(VertexRef const &n) const {
  assert(has(n));
  // TODO: fixme
  // Return the other vertex of the edge.
  return (n->label() == u_->label())
             ? v_
             : u_; // Comparar por label o ID es más seguro que por shared_ptr
                   // directamente
                   //
}

template <class T, class E>
typename Edge<T, E>::VertexRef &Edge<T, E>::other(VertexRef const &n) {
  assert(has(n));
  // TODO: fixme
  // Return the other vertex of the edge.
  return (n->label() == u_->label())
             ? v_
             : u_; // Comparar por label o ID es más seguro
                   //
}

template <class T, class E>
typename Edge<T, E>::VertexRef const &Edge<T, E>::first() const {
  // TODO: fixme
  return u_;
  //
}

template <class T, class E>
typename Edge<T, E>::VertexRef &Edge<T, E>::first() {
  // TODO: fixme
  return u_;
  //
}

template <class T, class E>
typename Edge<T, E>::VertexRef const &Edge<T, E>::second() const {
  // TODO: fixme
  return v_;
  //
}

template <class T, class E>
typename Edge<T, E>::VertexRef &Edge<T, E>::second() {
  // TODO: fixme
  return v_;
  //
}

template <class T, class E> void Edge<T, E>::set_item(E const &v) {
  // TODO
  item_ = v;
  //
  assert(item() == v);
}

template <class T, class E> void Edge<T, E>::set_visited(bool new_state) {
  // TODO
  is_visited_ = new_state;
  //
  assert(!new_state || is_visited());
  assert(new_state || !is_visited());
}